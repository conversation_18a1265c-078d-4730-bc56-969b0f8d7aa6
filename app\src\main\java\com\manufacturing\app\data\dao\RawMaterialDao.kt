package com.manufacturing.app.data.dao

import androidx.room.*
import com.manufacturing.app.data.entity.RawMaterial
import kotlinx.coroutines.flow.Flow

@Dao
interface RawMaterialDao {
    @Query("SELECT * FROM raw_materials ORDER BY name ASC")
    fun getAllRawMaterials(): Flow<List<RawMaterial>>

    @Query("SELECT * FROM raw_materials WHERE id = :id")
    suspend fun getRawMaterialById(id: Long): RawMaterial?

    @Query("SELECT * FROM raw_materials WHERE currentStock <= minimumStock")
    fun getLowStockRawMaterials(): Flow<List<RawMaterial>>

    @Insert
    suspend fun insertRawMaterial(rawMaterial: RawMaterial): Long

    @Update
    suspend fun updateRawMaterial(rawMaterial: RawMaterial)

    @Query("UPDATE raw_materials SET currentStock = :stock WHERE id = :id")
    suspend fun updateRawMaterialStock(id: Long, stock: Double)

    @Delete
    suspend fun deleteRawMaterial(rawMaterial: RawMaterial)
}
