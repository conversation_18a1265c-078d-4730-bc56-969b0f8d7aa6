package com.manufacturing.app.data.dao

import androidx.room.*
import com.manufacturing.app.data.entity.Stock
import kotlinx.coroutines.flow.Flow

@Dao
interface StockDao {
    @Query("SELECT * FROM stocks ORDER BY lastUpdated DESC")
    fun getAllStocks(): Flow<List<Stock>>

    @Query("SELECT * FROM stocks WHERE productId = :productId")
    suspend fun getStockByProduct(productId: Long): Stock?

    @Query("SELECT * FROM stocks WHERE availableQuantity <= minimumStock")
    fun getLowStockItems(): Flow<List<Stock>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStock(stock: Stock): Long

    @Update
    suspend fun updateStock(stock: Stock)

    @Query("UPDATE stocks SET currentQuantity = :quantity, availableQuantity = :available WHERE productId = :productId")
    suspend fun updateStockQuantity(productId: Long, quantity: Int, available: Int)
}
