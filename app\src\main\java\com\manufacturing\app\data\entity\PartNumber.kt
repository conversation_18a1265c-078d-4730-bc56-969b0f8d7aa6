package com.manufacturing.app.data.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey
import java.util.Date

@Entity(
    tableName = "part_numbers",
    foreignKeys = [
        ForeignKey(
            entity = Product::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class PartNumber(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val partNumber: String,
    val serialNumber: String,
    val productId: Long,
    val batchNumber: String,
    val quantity: Int,
    val qrCode: String,
    val manufacturingDate: Date,
    val expiryDate: Date? = null,
    val status: String = "ACTIVE", // ACTIVE, USED, DAMAGED, EXPIRED
    val createdAt: Date = Date()
)
