package com.manufacturing.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "raw_materials")
data class RawMaterial(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val description: String,
    val unit: String, // kg, liter, piece, etc.
    val currentStock: Double,
    val minimumStock: Double,
    val maximumStock: Double,
    val unitPrice: Double,
    val supplier: String,
    val location: String,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)
