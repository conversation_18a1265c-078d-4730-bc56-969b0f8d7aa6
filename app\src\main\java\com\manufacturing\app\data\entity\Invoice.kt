package com.manufacturing.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "invoices")
data class Invoice(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val invoiceNumber: String,
    val customerName: String,
    val customerAddress: String,
    val customerPhone: String,
    val customerEmail: String,
    val invoiceDate: Date,
    val dueDate: Date,
    val subtotal: Double,
    val taxAmount: Double,
    val discountAmount: Double = 0.0,
    val totalAmount: Double,
    val status: String = "PENDING", // PENDING, PAID, OVERDUE, CANCELLED
    val paymentMethod: String? = null,
    val notes: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)
