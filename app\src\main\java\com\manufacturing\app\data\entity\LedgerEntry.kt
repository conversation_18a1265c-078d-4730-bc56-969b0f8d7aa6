package com.manufacturing.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "ledger_entries")
data class LedgerEntry(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val transactionId: String,
    val accountName: String,
    val accountType: String, // ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
    val description: String,
    val debitAmount: Double = 0.0,
    val creditAmount: Double = 0.0,
    val balance: Double,
    val referenceType: String, // INVOICE, PURCHASE, STOCK_MOVEMENT, MANUAL
    val referenceId: Long? = null,
    val transactionDate: Date,
    val createdAt: Date = Date(),
    val createdBy: String
)
