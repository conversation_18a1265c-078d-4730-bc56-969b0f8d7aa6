package com.manufacturing.app.data.dao

import androidx.room.*
import com.manufacturing.app.data.entity.PartNumber
import kotlinx.coroutines.flow.Flow

@Dao
interface PartNumberDao {
    @Query("SELECT * FROM part_numbers ORDER BY createdAt DESC")
    fun getAllPartNumbers(): Flow<List<PartNumber>>

    @Query("SELECT * FROM part_numbers WHERE productId = :productId ORDER BY createdAt DESC")
    fun getPartNumbersByProduct(productId: Long): Flow<List<PartNumber>>

    @Query("SELECT * FROM part_numbers WHERE partNumber = :partNumber")
    suspend fun getPartNumberByCode(partNumber: String): PartNumber?

    @Query("SELECT * FROM part_numbers WHERE qrCode = :qrCode")
    suspend fun getPartNumberByQR(qrCode: String): PartNumber?

    @Query("SELECT COUNT(*) FROM part_numbers WHERE productId = :productId AND DATE(manufacturingDate) = DATE(:date)")
    suspend fun getPartCountForProductAndDate(productId: Long, date: String): Int

    @Insert
    suspend fun insertPartNumber(partNumber: PartNumber): Long

    @Insert
    suspend fun insertPartNumbers(partNumbers: List<PartNumber>)

    @Update
    suspend fun updatePartNumber(partNumber: PartNumber)

    @Query("UPDATE part_numbers SET status = :status WHERE id = :id")
    suspend fun updatePartNumberStatus(id: Long, status: String)
}
